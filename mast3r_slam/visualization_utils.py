import functools
import imgui
import matplotlib
import torch
import numpy as np
from in3d.geometry import LineGeometry
from mast3r_slam.frame import SharedKeyframes
from mast3r_slam.global_opt import FactorGraph
import open3d as o3d
import plotly.graph_objects as go
from mast3r_slam.geometry import constrain_points_to_ray


@functools.cache
def get_colormap(colormap):
    colormap = matplotlib.colormaps[colormap]
    return colormap(np.linspace(0, 1, 256))[:, :3]


def depth2rgb(depth, min=None, max=None, colormap="turbo", add_alpha=False, alpha=1.0):
    # depth: HxW
    dmin = np.nanmin(depth) if min is None else min
    dmax = np.nanmax(depth) if max is None else max
    d = (depth - dmin) / np.maximum((dmax - dmin), 1e-8)
    d = np.clip(d * 255, 0, 255).astype(np.int32)
    img = get_colormap(colormap)[d].astype(np.float32)
    if add_alpha:
        img = np.concatenate([img, alpha * np.ones_like(img[..., :1])], axis=-1)
    return np.ascontiguousarray(img)


class Frustums(LineGeometry):
    def __init__(self, program):
        super().__init__()
        self.program = program
        self.lines = []
        self.colors = []
        self.frustum = self.make_frustum(1, 1)

    def make_frustum(self, h, w):
        self.aspect_ratio = float(w / h)
        origin = [0.0, 0.0, 0.0]
        topleft = [-self.aspect_ratio, -1.0, 1.0]
        topright = [self.aspect_ratio, -1.0, 1.0]
        bottomleft = [-self.aspect_ratio, 1.0, 1.0]
        bottomright = [self.aspect_ratio, 1.0, 1.0]
        self.frustum = np.array(
            [
                origin,
                topleft,
                origin,
                topright,
                origin,
                bottomleft,
                origin,
                bottomright,
                topleft,
                topright,
                topright,
                bottomright,
                bottomright,
                bottomleft,
                bottomleft,
                topleft,
            ],
            dtype=np.float32,
        )

    def add(self, T_WC, thickness=3, scale=1, color=None):
        frustum = T_WC.act(torch.from_numpy(self.frustum * scale)).numpy()
        thickness = np.ones_like(frustum[..., :1]) * thickness
        frustum = np.concatenate([frustum, thickness], axis=-1).reshape(-1, 4)
        color = [1.0, 1.0, 1.0, 1.0] if color is None else color
        colors = np.tile(color, (frustum.shape[0], 1)).astype(np.float32)
        self.lines.append(frustum)
        self.colors.append(colors)

    def render(self, camera, mode=None):
        if len(self.lines) == 0:
            return
        self.lines = np.concatenate(self.lines, axis=0)
        self.colors = np.concatenate(self.colors, axis=0)
        self.clear()
        super().render(camera, mode=mode)
        self.lines = []
        self.colors = []


class Lines(LineGeometry):
    def __init__(self, program):
        super().__init__()
        self.program = program
        self.lines = []
        self.colors = []

    def add(self, start, end, thickness=1, color=None):
        start = start.reshape(-1, 3).astype(np.float32)
        end = end.reshape(-1, 3).astype(np.float32)

        thickness = np.ones_like(start[..., :1]) * thickness
        start_xyzw = np.concatenate([start, thickness], axis=-1)
        end_xyzw = np.concatenate([end, thickness], axis=-1)
        line = np.concatenate([start_xyzw, end_xyzw], axis=1).reshape(-1, 4)
        if isinstance(color, np.ndarray):  # TODO Bit hacky!
            colors = color.reshape(-1, 4).astype(np.float32)
        else:
            color = [1.0, 1.0, 1.0, 1.0] if color is None else color
            colors = np.tile(color, (line.shape[0], 1)).astype(np.float32)

        # make sure that the dimensions match!
        assert line.shape[0] == colors.shape[0]
        assert line.shape[1] == 4 and colors.shape[1] == 4

        self.lines.append(line)
        self.colors.append(colors)

    def render(self, camera, mode=None):
        if len(self.lines) == 0:
            return
        self.lines = np.concatenate(self.lines, axis=0)
        self.colors = np.concatenate(self.colors, axis=0)
        self.clear()
        super().render(camera, mode=mode)
        self.lines = []
        self.colors = []


def image_with_text(img, size, text, same_line=False):
    # check if the img is too small to render
    if size[0] < 16:
        return
    text_cursor_pos = imgui.get_cursor_pos()
    imgui.image(img.texture.glo, *size)
    if same_line:
        imgui.same_line()
    next_cursor_pos = imgui.get_cursor_pos()
    imgui.set_cursor_pos(text_cursor_pos)
    imgui.text(text)
    imgui.set_cursor_pos(next_cursor_pos)


def show(keyframes: SharedKeyframes, factor_graph: FactorGraph, show_cloudpoints=True, show_colors=True, show_poses=True, show_arrows=True, voxel_size=0.1, highlight_nodes=[], use_calib=False, max_points_per_frame=10000):

    if len(keyframes) == 0:
        return

    # 创建plotly图形对象
    fig = go.Figure()

    # 只有在show_cloudpoints为True时才处理和显示点云
    if show_cloudpoints:
        # 在同一个图中显示点云和连接图
        pointclouds = []
        colors = []
        for frame in keyframes:
            if use_calib:
                X_canon = constrain_points_to_ray(frame.img_shape.flatten()[:2], frame.X_canon[None], frame.K)
                frame.X_canon = X_canon.squeeze(0)
            pW = frame.T_WC.act(frame.X_canon).cpu().numpy().reshape(-1, 3)
            color = (frame.uimg.cpu().numpy() * 255).astype(np.uint8).reshape(-1, 3)
            valid = (frame.get_average_conf().cpu().numpy().astype(np.float32).reshape(-1) > 0.5)
            # 增加更严格的置信度过滤
            valid = valid & (frame.get_average_conf().cpu().numpy().astype(np.float32).reshape(-1) > 0.7)
            # 随机采样，只保留一部分点
            if np.sum(valid) > max_points_per_frame:
                indices = np.where(valid)[0]
                sample_size = min(max_points_per_frame, len(indices))
                sample_indices = np.random.choice(indices, sample_size, replace=False)
                new_valid = np.zeros_like(valid)
                new_valid[sample_indices] = True
                valid = new_valid
            pointclouds.append(pW[valid])
            colors.append(color[valid])
        pointclouds = np.concatenate(pointclouds, axis=0)
        colors = np.concatenate(colors, axis=0)
        pcd = o3d.geometry.PointCloud()
        pcd.points = o3d.utility.Vector3dVector(pointclouds)
        pcd.colors = o3d.utility.Vector3dVector(colors)
        pcd = pcd.voxel_down_sample(voxel_size=voxel_size)
        points = np.asarray(pcd.points)
        colors = np.asarray(pcd.colors) if pcd.has_colors() else None
        colors = colors.astype(np.uint8)

        # 创建plotly 3D散点图
        # 根据show_colors参数决定是否使用颜色
        if show_colors and colors is not None:
            colors_rgb = [f'rgb({r},{g},{b})' for r, g, b in colors]
            marker_dict = dict(size=1.5, color=colors_rgb)
        else:
            marker_dict = dict(size=1.5, color='lightgray')

        pointcloud_trace = go.Scatter3d(
            x=points[:, 0],
            y=points[:, 1],
            z=points[:, 2],
            mode='markers',
            marker=marker_dict,
            showlegend=False
        )
        fig.add_trace(pointcloud_trace)

    if show_poses:
        nodes = {}
        camera_poses = {}
        for i, frame in zip(factor_graph.vertices, factor_graph.frames):
            # Extract position
            pose_data = frame.T_WC.data.cpu().numpy()[0]
            x, y, z = pose_data[:3].tolist()
            nodes[i] = (x, y, z)

            # Extract rotation matrix from Sim3 transformation
            # Sim3 format: [tx, ty, tz, qx, qy, qz, qw, s] where q is quaternion and s is scale
            qx, qy, qz, qw = pose_data[3:7]

            # Convert quaternion to rotation matrix
            R = np.array([
                [1 - 2 * (qy**2 + qz**2), 2 * (qx*qy - qw*qz), 2 * (qx*qz + qw*qy)],
                [2 * (qx*qy + qw*qz), 1 - 2 * (qx**2 + qz**2), 2 * (qy*qz - qw*qx)],
                [2 * (qx*qz - qw*qy), 2 * (qy*qz + qw*qx), 1 - 2 * (qx**2 + qy**2)]
            ])

            camera_poses[i] = {'position': np.array([x, y, z]), 'rotation': R}

        edges = factor_graph.edges

        highlight_nodes = set(highlight_nodes)
        if len(highlight_nodes) == 0:
            if nodes:
                highlight_nodes.add(max(nodes.keys()))
        highlight_connected_nodes = set()
        for edge in edges:
            if edge[0] in highlight_nodes:
                highlight_connected_nodes.add(edge[1])
            elif edge[1] in highlight_nodes:
                highlight_connected_nodes.add(edge[0])

        # Create edge traces
        edge_traces = []
        for edge in edges:
            start_node = nodes[edge[0]]
            end_node = nodes[edge[1]]

            # 如果连线涉及最后一个节点，使用绿色；否则使用蓝色
            if edge[0] in highlight_nodes or edge[1] in highlight_nodes:
                line_color = 'red'
                line_width = 5  # 稍微加粗绿色线条
            else:
                line_color = 'blue'
                line_width = 4

            edge_trace = go.Scatter3d(
                x=[start_node[0], end_node[0], None],
                y=[start_node[1], end_node[1], None],
                z=[start_node[2], end_node[2], None],
                mode='lines',
                line=dict(width=line_width, color=line_color),
                hoverinfo='none',
                showlegend=False
            )
            edge_traces.append(edge_trace)

        # Create camera pose arrows instead of simple nodes
        arrow_traces = []
        arrow_length = 0.4  # Adjust this to change arrow size

        for i, pose_info in camera_poses.items():
            pos = pose_info['position']
            R = pose_info['rotation']

            # Camera coordinate system: typically Z-axis points forward (camera direction)
            # X-axis points right, Y-axis points down
            forward_dir = R[:, 2]  # Z-axis (camera forward direction)
            right_dir = R[:, 0]    # X-axis (camera right direction)
            up_dir = -R[:, 1]      # -Y-axis (camera up direction, negative because Y typically points down)

            # 确定箭头颜色：最后一个节点用红色，与最后节点相连的用橙色，其他用蓝色
            if i in highlight_nodes:
                arrow_color = 'red'
                marker_color = 'darkred'
            elif i in highlight_connected_nodes:
                arrow_color = 'orange'
                marker_color = 'darkorange'
            else:
                arrow_color = 'gray'
                marker_color = 'darkgray'

            if show_arrows:
                # Create arrow shaft (forward direction)
                arrow_end = pos + forward_dir * arrow_length
                arrow_trace = go.Scatter3d(
                    x=[pos[0], arrow_end[0], None],
                    y=[pos[1], arrow_end[1], None],
                    z=[pos[2], arrow_end[2], None],
                    mode='lines',
                    line=dict(width=1, color=arrow_color),
                    name=f'Camera {i}',
                    hoverinfo='name',
                    showlegend=False
                )
                arrow_traces.append(arrow_trace)

                # Create arrow head (cone-like structure)
                head_length = arrow_length * 0.3
                head_width = arrow_length * 0.1

                # Create arrow head using two lines forming a cone
                head_base = arrow_end - forward_dir * head_length
                head_point1 = head_base + right_dir * head_width + up_dir * head_width
                head_point2 = head_base - right_dir * head_width + up_dir * head_width
                head_point3 = head_base + right_dir * head_width - up_dir * head_width
                head_point4 = head_base - right_dir * head_width - up_dir * head_width

                # Arrow head lines
                for head_point in [head_point1, head_point2, head_point3, head_point4]:
                    head_trace = go.Scatter3d(
                        x=[arrow_end[0], head_point[0], None],
                        y=[arrow_end[1], head_point[1], None],
                        z=[arrow_end[2], head_point[2], None],
                        mode='lines',
                        line=dict(width=1, color=arrow_color),
                        showlegend=False,
                        hoverinfo='none'
                    )
                    arrow_traces.append(head_trace)

            # Add camera position marker
            pos_trace = go.Scatter3d(
                x=[pos[0]],
                y=[pos[1]],
                z=[pos[2]],
                mode='markers+text',
                marker=dict(size=6, color=marker_color, symbol='circle'),
                text=[str(i)],
                textposition="top center",
                showlegend=False,
                hoverinfo='text'
            )
            arrow_traces.append(pos_trace)

        fig.add_traces(edge_traces)
        fig.add_traces(arrow_traces)

    # 更新布局，隐藏图例并优化性能
    fig.update_layout(
        showlegend=False,
        scene=dict(
            aspectmode='data',  # 保持数据原始比例
        ),
        uirevision='same',  # 保持视图状态，避免重新计算
    )
    
    # 使用更高效的渲染模式
    fig.update_traces(
        selector=dict(type='scatter3d'),
        hoverinfo='none'  # 禁用悬停信息以提高性能
    )
    
    # fig.show(renderer="browser")  # 使用浏览器渲染，通常更快
    fig.show()
