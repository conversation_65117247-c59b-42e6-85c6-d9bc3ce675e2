import PIL
import numpy as np
import torch
import einops

import mast3r.utils.path_to_dust3r  # noqa
from dust3r.utils.image import ImgNorm
from mast3r.model import AsymmetricMASt3R
from mast3r_slam.retrieval_database import RetrievalDatabase
from mast3r_slam.config import config
import mast3r_slam.matching as matching

import time


def load_mast3r(path=None, device="cuda"):
    weights_path = (
        "checkpoints/MASt3R_ViTLarge_BaseDecoder_512_catmlpdpt_metric.pth"
        if path is None
        else path
    )
    model = AsymmetricMASt3R.from_pretrained(weights_path).to(device)
    return model


def load_retriever(mast3r_model, retriever_path=None, device="cuda"):
    retriever_path = (
        "checkpoints/MASt3R_ViTLarge_BaseDecoder_512_catmlpdpt_metric_retrieval_trainingfree.pth"
        if retriever_path is None
        else retriever_path
    )
    retriever = RetrievalDatabase(retriever_path, backbone=mast3r_model, device=device)
    return retriever


@torch.inference_mode
def decoder(model, feat1, feat2, pos1, pos2, shape1, shape2):
    dec1, dec2 = model._decoder(feat1, pos1, feat2, pos2)
    with torch.amp.autocast(enabled=False, device_type="cuda"):
        res1 = model._downstream_head(1, [tok.float() for tok in dec1], shape1)
        res2 = model._downstream_head(2, [tok.float() for tok in dec2], shape2)
    return res1, res2


def downsample(X, C, D, Q):
    downsample = config["dataset"]["img_downsample"]
    if downsample > 1:
        # C and Q: (...xHxW)
        # X and D: (...xHxWxF)
        X = X[..., ::downsample, ::downsample, :].contiguous()
        C = C[..., ::downsample, ::downsample].contiguous()
        D = D[..., ::downsample, ::downsample, :].contiguous()
        Q = Q[..., ::downsample, ::downsample].contiguous()
    return X, C, D, Q


@torch.inference_mode
def mast3r_symmetric_inference(model, frame_i, frame_j):
    if frame_i.feat is None:
        frame_i.feat, frame_i.pos, _ = model._encode_image(
            frame_i.img, frame_i.img_true_shape
        )
    if frame_j.feat is None:
        frame_j.feat, frame_j.pos, _ = model._encode_image(
            frame_j.img, frame_j.img_true_shape
        )

    feat1, feat2 = frame_i.feat, frame_j.feat
    pos1, pos2 = frame_i.pos, frame_j.pos
    shape1, shape2 = frame_i.img_true_shape, frame_j.img_true_shape

    res11, res21 = decoder(model, feat1, feat2, pos1, pos2, shape1, shape2)
    res22, res12 = decoder(model, feat2, feat1, pos2, pos1, shape2, shape1)
    res = [res11, res21, res22, res12]
    X, C, D, Q = zip(
        *[(r["pts3d"][0], r["conf"][0], r["desc"][0], r["desc_conf"][0]) for r in res]
    )
    # 4xhxwxc
    X, C, D, Q = torch.stack(X), torch.stack(C), torch.stack(D), torch.stack(Q)
    X, C, D, Q = downsample(X, C, D, Q)
    return X, C, D, Q


# NOTE: Assumes img shape the same
@torch.inference_mode
def mast3r_decode_symmetric_batch(
    model, feat_i, pos_i, feat_j, pos_j, shape_i, shape_j
):
    B = feat_i.shape[0]
    X, C, D, Q = [], [], [], []
    for b in range(B):
        feat1 = feat_i[b][None]
        feat2 = feat_j[b][None]
        pos1 = pos_i[b][None]
        pos2 = pos_j[b][None]
        res11, res21 = decoder(model, feat1, feat2, pos1, pos2, shape_i[b], shape_j[b])
        res22, res12 = decoder(model, feat2, feat1, pos2, pos1, shape_j[b], shape_i[b])
        res = [res11, res21, res22, res12]
        Xb, Cb, Db, Qb = zip(
            *[
                (r["pts3d"][0], r["conf"][0], r["desc"][0], r["desc_conf"][0])
                for r in res
            ]
        )
        X.append(torch.stack(Xb, dim=0))
        C.append(torch.stack(Cb, dim=0))
        D.append(torch.stack(Db, dim=0))
        Q.append(torch.stack(Qb, dim=0))

    X, C, D, Q = (
        torch.stack(X, dim=1),
        torch.stack(C, dim=1),
        torch.stack(D, dim=1),
        torch.stack(Q, dim=1),
    )
    X, C, D, Q = downsample(X, C, D, Q)
    return X, C, D, Q


@torch.inference_mode
def mast3r_inference_mono(model, frame):
    if frame.feat is None:
        frame.feat, frame.pos, _ = model._encode_image(frame.img, frame.img_true_shape)

    feat = frame.feat
    pos = frame.pos
    shape = frame.img_true_shape

    res11, res21 = decoder(model, feat, feat, pos, pos, shape, shape)
    res = [res11, res21]
    X, C, D, Q = zip(
        *[(r["pts3d"][0], r["conf"][0], r["desc"][0], r["desc_conf"][0]) for r in res]
    )
    # 4xhxwxc
    X, C, D, Q = torch.stack(X), torch.stack(C), torch.stack(D), torch.stack(Q)
    X, C, D, Q = downsample(X, C, D, Q)

    Xii, Xji = einops.rearrange(X, "b h w c -> b (h w) c")
    Cii, Cji = einops.rearrange(C, "b h w -> b (h w) 1")

    return Xii, Cii


def mast3r_match_symmetric(model, feat_i, pos_i, feat_j, pos_j, shape_i, shape_j):
    X, C, D, Q = mast3r_decode_symmetric_batch(
        model, feat_i, pos_i, feat_j, pos_j, shape_i, shape_j
    )

    # Ordering 4xbxhxwxc
    b = X.shape[1]

    Xii, Xji, Xjj, Xij = X[0], X[1], X[2], X[3]
    Dii, Dji, Djj, Dij = D[0], D[1], D[2], D[3]
    Qii, Qji, Qjj, Qij = Q[0], Q[1], Q[2], Q[3]

    # Always matching both
    X11 = torch.cat((Xii, Xjj), dim=0)
    X21 = torch.cat((Xji, Xij), dim=0)
    D11 = torch.cat((Dii, Djj), dim=0)
    D21 = torch.cat((Dji, Dij), dim=0)

    # tic()
    idx_1_to_2, valid_match_2 = matching.match(X11, X21, D11, D21)
    # toc("Match")

    # TODO: Avoid this
    match_b = X11.shape[0] // 2
    idx_i2j = idx_1_to_2[:match_b]
    idx_j2i = idx_1_to_2[match_b:]
    valid_match_j = valid_match_2[:match_b]
    valid_match_i = valid_match_2[match_b:]

    return (
        idx_i2j,
        idx_j2i,
        valid_match_j,
        valid_match_i,
        Qii.view(b, -1, 1),
        Qjj.view(b, -1, 1),
        Qji.view(b, -1, 1),
        Qij.view(b, -1, 1),
    )


@torch.inference_mode
def mast3r_asymmetric_inference(model, frame_i, frame_j):
    if frame_i.feat is None:
        frame_i.feat, frame_i.pos, _ = model._encode_image(
            frame_i.img, frame_i.img_true_shape
        )
    if frame_j.feat is None:
        frame_j.feat, frame_j.pos, _ = model._encode_image(
            frame_j.img, frame_j.img_true_shape
        )

    feat1, feat2 = frame_i.feat, frame_j.feat
    pos1, pos2 = frame_i.pos, frame_j.pos
    shape1, shape2 = frame_i.img_true_shape, frame_j.img_true_shape

    res11, res21 = decoder(model, feat1, feat2, pos1, pos2, shape1, shape2)
    res = [res11, res21]
    X, C, D, Q = zip(
        *[(r["pts3d"][0], r["conf"][0], r["desc"][0], r["desc_conf"][0]) for r in res]
    )
    # 4xhxwxc
    X, C, D, Q = torch.stack(X), torch.stack(C), torch.stack(D), torch.stack(Q)
    X, C, D, Q = downsample(X, C, D, Q)
    return X, C, D, Q


def mast3r_match_asymmetric(model, frame_i, frame_j, idx_i2j_init=None):
    X, C, D, Q = mast3r_asymmetric_inference(model, frame_i, frame_j)

    b, h, w = X.shape[:-1]
    # 2 outputs per inference
    b = b // 2

    Xii, Xji = X[:b], X[b:]
    Cii, Cji = C[:b], C[b:]
    Dii, Dji = D[:b], D[b:]
    Qii, Qji = Q[:b], Q[b:]

    idx_i2j, valid_match_j = matching.match(
        Xii, Xji, Dii, Dji, idx_1_to_2_init=idx_i2j_init
    )

    # How rest of system expects it
    Xii, Xji = einops.rearrange(X, "b h w c -> b (h w) c")
    Cii, Cji = einops.rearrange(C, "b h w -> b (h w) 1")
    Dii, Dji = einops.rearrange(D, "b h w c -> b (h w) c")
    Qii, Qji = einops.rearrange(Q, "b h w -> b (h w) 1")

    return idx_i2j, valid_match_j, Xii, Cii, Qii, Xji, Cji, Qji


def mast3r_estimate_pose(model, frame_i, frame_j):
    """
    使用Mast3r进行位姿估计

    Args:
        model: Mast3r model 实例
        frame_i: 帧i  frame.Frame
        frame_j: 帧j  frame.Frame

    Returns:
        T_ij: 位姿估计结果 (从frame_j到frame_i的变换)
        T_ji: 位姿估计结果 (从frame_i到frame_j的变换)
        info: 估计信息和误差统计
    """
    import torch
    import lietorch
    import numpy as np
    from scipy.spatial.transform import Rotation
    import random

    try:
        # 使用对称推理获取点云和置信度
        t0 = time.time()
        X, C, D, Q = mast3r_symmetric_inference(model, frame_i, frame_j)
        print(f"Symmetric Inference Time: {time.time() - t0:.2f}s")

        # X的顺序: [Xii, Xji, Xjj, Xij]
        # 其中 i=frame_i, j=frame_j
        Xii = X[0]  # frame_i在frame_i坐标系下的点云
        Xji = X[1]  # frame_i在frame_j坐标系下的点云
        Xjj = X[2]  # frame_j在frame_j坐标系下的点云
        Xij = X[3]  # frame_j在frame_i坐标系下的点云

        Cii = C[0]  # frame_i点云的置信度
        Cji = C[1]
        Cjj = C[2]
        Cij = C[3]

        # 使用置信度阈值过滤点
        conf_threshold = 0.5

        # 估计T_ji (从frame_i到frame_j的变换)
        valid_ii = Cii > conf_threshold
        valid_ji = Cji > conf_threshold
        valid_mask_ji = valid_ii & valid_ji
        # 估计T_ij (从frame_j到frame_i的变换)
        valid_jj = Cjj > conf_threshold
        valid_ij = Cij > conf_threshold
        valid_mask_ij = valid_jj & valid_ij

        # 展平点云数据
        Xii_flat = Xii.view(-1, 3)  # [H*W, 3]
        Xji_flat = Xji.view(-1, 3)
        Xjj_flat = Xjj.view(-1, 3)
        Xij_flat = Xij.view(-1, 3)

        valid_mask_ji_flat = valid_mask_ji.view(-1)
        valid_mask_ij_flat = valid_mask_ij.view(-1)

        # 估计T_ji: 从frame_i坐标系到frame_j坐标系
        T_ji = None
        T_ij = None
        info = {
            'num_valid_points_ji': 0,
            'num_valid_points_ij': 0,
            'inliers_ji': 0,
            'inliers_ij': 0,
            'success_ji': False,
            'success_ij': False,
            'error_ji': None,
            'error_ij': None
        }

        # 估计T_ji
        if valid_mask_ji_flat.sum() > 10:  # 至少需要10个有效点
            pts_i = Xii_flat[valid_mask_ji_flat]  # frame_i坐标系下的点
            pts_j_from_i = Xji_flat[valid_mask_ji_flat]  # 对应的frame_j坐标系下的点

            t0 = time.time()
            T_ji = _estimate_transform_ransac(pts_i, pts_j_from_i)
            print(f"RANSAC耗时: {time.time() - t0:.2f}s")
            info['num_valid_points_ji'] = valid_mask_ji_flat.sum().item()

            if T_ji is not None:
                try:
                    # 使用geometry.py中的act_Sim3函数来处理形状兼容性
                    from mast3r_slam.geometry import act_Sim3
                    pts_transformed = act_Sim3(T_ji, pts_i.double()).float()
                    distances = torch.norm(pts_transformed - pts_j_from_i, dim=1)
                    info['inliers_ji'] = (distances < 0.1).sum().item()
                    info['success_ji'] = True
                    info['error_ji'] = distances.mean().item()
                except Exception as e:
                    T_ji = None

        # 估计T_ij
        if valid_mask_ij_flat.sum() > 10:  # 至少需要10个有效点
            pts_j = Xjj_flat[valid_mask_ij_flat]  # frame_j坐标系下的点
            pts_i_from_j = Xij_flat[valid_mask_ij_flat]  # 对应的frame_i坐标系下的点

            t0 = time.time()
            T_ij = _estimate_transform_ransac(pts_j, pts_i_from_j)
            print(f"RANSAC耗时: {time.time() - t0:.2f}s")
            info['num_valid_points_ij'] = valid_mask_ij_flat.sum().item()

            if T_ij is not None:
                try:
                    # 使用geometry.py中的act_Sim3函数来处理形状兼容性
                    from mast3r_slam.geometry import act_Sim3
                    pts_transformed = act_Sim3(T_ij, pts_j.double()).float()
                    distances = torch.norm(pts_transformed - pts_i_from_j, dim=1)
                    info['inliers_ij'] = (distances < 0.1).sum().item()
                    info['success_ij'] = True
                    info['error_ij'] = distances.mean().item()
                except Exception as e:
                    T_ij = None

        return T_ij, T_ji, info

    except Exception as e:
        error_info = {
            'num_valid_points_ji': 0,
            'num_valid_points_ij': 0,
            'inliers_ji': 0,
            'inliers_ij': 0,
            'success_ji': False,
            'success_ij': False,
            'error_ji': None,
            'error_ij': None,
            'exception': str(e)
        }
        return None, None, error_info


def _estimate_transform_ransac(pts_src, pts_dst, max_iterations=50, threshold=0.1):
    """
    使用RANSAC估计从src到dst的Sim3变换

    Args:
        pts_src: 源坐标系下的点 [N, 3]
        pts_dst: 目标坐标系下的点 [N, 3]
        max_iterations: RANSAC最大迭代次数
        threshold: 内点阈值

    Returns:
        lietorch.Sim3: 估计的变换，失败返回None
    """
    import torch
    import lietorch
    import random
    from scipy.spatial.transform import Rotation
    import numpy as np

    try:
        N = pts_src.shape[0]
        if N < 4:
            return None

        best_inliers = 0
        best_transform = None

        # 先尝试使用所有点进行初始估计
        try:
            transform_all = _estimate_sim3_transform(pts_src, pts_dst)
            if transform_all is not None:
                # 测试所有点的变换
                from mast3r_slam.geometry import act_Sim3
                pts_transformed = act_Sim3(transform_all, pts_src.double()).float()
                distances = torch.norm(pts_transformed - pts_dst, dim=1)
                inliers_all = (distances < threshold).sum().item()

                if inliers_all > N * 0.5:  # 如果超过50%的点是内点，直接返回
                    return transform_all
        except Exception as e:
            pass

        # 如果全点估计失败，使用RANSAC
        for iteration in range(max_iterations):
            try:
                # 随机选择4个点
                indices = random.sample(range(N), min(4, N))
                src_sample = pts_src[indices]
                dst_sample = pts_dst[indices]

                # 检查点是否退化（共线等）
                if _check_degenerate_points(src_sample) or _check_degenerate_points(dst_sample):
                    continue

                # 估计变换
                transform = _estimate_sim3_transform(src_sample, dst_sample)
                if transform is None:
                    continue

                # 计算内点
                from mast3r_slam.geometry import act_Sim3
                pts_transformed = act_Sim3(transform, pts_src.double()).float()
                distances = torch.norm(pts_transformed - pts_dst, dim=1)
                inliers = (distances < threshold).sum().item()

                if inliers > best_inliers:
                    best_inliers = inliers
                    best_transform = transform

                    # 早期停止条件
                    if inliers > N * 0.8:  # 如果超过80%的点是内点
                        break

            except Exception as e:
                continue

        # 检查最终结果
        min_inliers = max(4, N * 0.2)  # 至少4个点或20%的点
        if best_transform is None or best_inliers < min_inliers:
            return None

        return best_transform

    except Exception as e:
        return None


def _estimate_sim3_transform(pts_src, pts_dst):
    """
    估计Sim3变换（相似变换：旋转+平移+缩放）
    使用Horn's method for similarity transformation
    """
    import torch
    import lietorch
    from scipy.spatial.transform import Rotation

    try:
        N = pts_src.shape[0]
        if N < 3:
            return None

        # 计算质心
        src_centroid = pts_src.mean(dim=0)
        dst_centroid = pts_dst.mean(dim=0)

        # 中心化
        src_centered = pts_src - src_centroid
        dst_centered = pts_dst - dst_centroid

        # 计算尺度 (使用RMS距离)
        src_scale = torch.sqrt((src_centered ** 2).sum() / N)
        dst_scale = torch.sqrt((dst_centered ** 2).sum() / N)

        if src_scale < 1e-6 or dst_scale < 1e-6:
            return None

        scale = (dst_scale / src_scale).item()

        # 归一化点云用于旋转估计
        src_normalized = src_centered / src_scale
        dst_normalized = dst_centered / dst_scale

        # 使用Kabsch算法估计旋转
        H = src_normalized.T @ dst_normalized
        U, S, Vt = torch.linalg.svd(H)
        R = Vt.T @ U.T

        # 确保是正确的旋转矩阵
        if torch.det(R) < 0:
            Vt[-1, :] *= -1
            R = Vt.T @ U.T

        # 计算平移
        t = dst_centroid - scale * (R @ src_centroid)

        # 构建Sim3变换
        # lietorch.Sim3的格式是 [tx, ty, tz, qx, qy, qz, qw, scale]
        R_np = R.cpu().numpy()
        quat = Rotation.from_matrix(R_np).as_quat()  # [x, y, z, w]
        # lietorch格式是 [x, y, z, w]
        quat_lie = torch.tensor([quat[0], quat[1], quat[2], quat[3]],
                              device=pts_src.device, dtype=torch.float64)

        # lietorch.Sim3期望的数据格式: [tx, ty, tz, qx, qy, qz, qw, scale]
        sim3_data = torch.cat([
            t.double(),
            quat_lie,
            torch.tensor([scale], device=pts_src.device, dtype=torch.float64)
        ]).unsqueeze(0)  # [1, 8]

        return lietorch.Sim3(sim3_data)

    except Exception as e:
        return None


def _check_degenerate_points(pts):
    """
    检查点是否退化（共线、共面等）
    """
    try:
        if pts.shape[0] < 3:
            return False

        # 检查是否所有点都相同
        if torch.allclose(pts, pts[0:1], atol=1e-6):
            return True

        # 检查是否共线
        if pts.shape[0] >= 3:
            v1 = pts[1] - pts[0]
            v2 = pts[2] - pts[0]
            cross = torch.linalg.cross(v1, v2)
            if torch.norm(cross) < 1e-6:
                return True

        return False
    except:
        return True


def _resize_pil_image(img, long_edge_size):
    S = max(img.size)
    if S > long_edge_size:
        interp = PIL.Image.LANCZOS
    elif S <= long_edge_size:
        interp = PIL.Image.BICUBIC
    new_size = tuple(int(round(x * long_edge_size / S)) for x in img.size)
    return img.resize(new_size, interp)


def resize_img(img, size, square_ok=False, return_transformation=False):
    assert size == 224 or size == 512
    # numpy to PIL format
    img = PIL.Image.fromarray(np.uint8(img * 255))
    W1, H1 = img.size
    if size == 224:
        # resize short side to 224 (then crop)
        img = _resize_pil_image(img, round(size * max(W1 / H1, H1 / W1)))
    else:
        # resize long side to 512
        img = _resize_pil_image(img, size)
    W, H = img.size
    cx, cy = W // 2, H // 2
    if size == 224:
        half = min(cx, cy)
        img = img.crop((cx - half, cy - half, cx + half, cy + half))
    else:
        halfw, halfh = ((2 * cx) // 16) * 8, ((2 * cy) // 16) * 8
        if not (square_ok) and W == H:
            halfh = 3 * halfw / 4
        img = img.crop((cx - halfw, cy - halfh, cx + halfw, cy + halfh))

    res = dict(
        img=ImgNorm(img)[None],
        true_shape=np.int32([img.size[::-1]]),
        unnormalized_img=np.asarray(img),
    )
    if return_transformation:
        scale_w = W1 / W
        scale_h = H1 / H
        half_crop_w = (W - img.size[0]) / 2
        half_crop_h = (H - img.size[1]) / 2
        return res, (scale_w, scale_h, half_crop_w, half_crop_h)

    return res
