#!/usr/bin/env python3
"""
示例：如何使用 mast3r_forward 函数

这个脚本展示了如何使用 mast3r_forward 函数基于一个基础帧来预测新图像的Frame对象。
mast3r_forward 直接利用MASt3R模型进行预测，不做优化。
"""

import torch
import numpy as np
import PIL.Image
import lietorch
from mast3r_slam.mast3r_utils import load_mast3r, mast3r_forward
from mast3r_slam.frame import create_frame
from mast3r_slam.config import load_config

def example_usage():
    """演示 mast3r_forward 的基本用法"""
    
    # 1. 加载配置和模型
    print("Loading configuration and model...")
    load_config("config/base.yaml")
    
    device = "cuda" if torch.cuda.is_available() else "cpu"
    model = load_mast3r(device=device)
    print(f"Model loaded on {device}")
    
    # 2. 创建或加载基础帧
    print("Creating base frame...")
    
    # 这里我们创建一个随机图像作为示例
    # 在实际应用中，你会从文件或相机加载真实图像
    img_size = 512
    base_img_array = np.random.rand(img_size, img_size, 3)
    
    # 创建基础帧的位姿（世界坐标系下）
    base_T_WC = lietorch.Sim3.Identity(1, device=device)
    
    # 创建基础帧
    base_frame = create_frame(0, base_img_array, base_T_WC, device=device)
    print(f"Base frame created: {base_frame}")
    
    # 3. 准备新图像
    print("Preparing new image...")
    
    # 创建一个新的图像（在实际应用中，这会是来自相机的新帧）
    new_img_array = np.random.rand(img_size, img_size, 3)
    new_img_pil = PIL.Image.fromarray((new_img_array * 255).astype(np.uint8))
    
    # 4. 使用 mast3r_forward 预测新帧
    print("Running mast3r_forward...")
    
    new_frame = mast3r_forward(model, base_frame, new_img_pil)
    
    if new_frame is not None:
        print("✓ Successfully predicted new frame!")
        print(f"New frame: {new_frame}")
        
        # 5. 检查预测结果
        print("\nPrediction results:")
        print(f"- Image shape: {new_frame.img.shape}")
        print(f"- Point cloud shape: {new_frame.X_canon.shape}")
        print(f"- Confidence shape: {new_frame.C.shape}")
        
        # 统计有效点
        valid_points = (new_frame.C > 0.1).sum()
        total_points = new_frame.C.shape[0]
        print(f"- Valid points (conf > 0.1): {valid_points}/{total_points} ({100*valid_points/total_points:.1f}%)")
        
        if valid_points > 0:
            print(f"- Point cloud range:")
            print(f"  X: [{new_frame.X_canon[:, 0].min():.3f}, {new_frame.X_canon[:, 0].max():.3f}]")
            print(f"  Y: [{new_frame.X_canon[:, 1].min():.3f}, {new_frame.X_canon[:, 1].max():.3f}]")
            print(f"  Z: [{new_frame.X_canon[:, 2].min():.3f}, {new_frame.X_canon[:, 2].max():.3f}]")
            print(f"- Confidence range: [{new_frame.C.min():.3f}, {new_frame.C.max():.3f}]")
        
        return new_frame
    else:
        print("✗ Failed to predict new frame")
        return None

def example_with_pose_estimation():
    """演示如何结合位姿估计使用 mast3r_forward"""
    
    print("\n" + "="*60)
    print("Example: mast3r_forward with pose estimation")
    print("="*60)
    
    # 加载配置和模型
    load_config("config/base.yaml")
    device = "cuda" if torch.cuda.is_available() else "cpu"
    model = load_mast3r(device=device)
    
    # 创建基础帧
    img_size = 512
    base_img_array = np.random.rand(img_size, img_size, 3)
    base_T_WC = lietorch.Sim3.Identity(1, device=device)
    base_frame = create_frame(0, base_img_array, base_T_WC, device=device)
    
    # 创建新图像
    new_img_array = np.random.rand(img_size, img_size, 3)
    new_img_pil = PIL.Image.fromarray((new_img_array * 255).astype(np.uint8))
    
    # 使用 mast3r_forward 预测新帧
    new_frame = mast3r_forward(model, base_frame, new_img_pil)
    
    if new_frame is not None:
        print("✓ New frame predicted successfully")
        
        # 在实际应用中，你可能想要：
        # 1. 使用位姿估计来优化新帧的位姿
        # 2. 将新帧添加到关键帧集合中
        # 3. 进行局部或全局优化
        
        print("Next steps in a real application:")
        print("1. Estimate pose using mast3r_estimate_pose or similar")
        print("2. Add to keyframe collection if needed")
        print("3. Perform local/global optimization")
        print("4. Update the map")
        
        return new_frame
    else:
        print("✗ Failed to predict new frame")
        return None

def main():
    """主函数"""
    print("MASt3R Forward Function Example")
    print("="*60)
    
    try:
        # 基本用法示例
        new_frame1 = example_usage()
        
        # 结合位姿估计的示例
        new_frame2 = example_with_pose_estimation()
        
        print("\n" + "="*60)
        print("Summary:")
        print(f"- Basic example: {'✓ Success' if new_frame1 is not None else '✗ Failed'}")
        print(f"- Pose estimation example: {'✓ Success' if new_frame2 is not None else '✗ Failed'}")
        print("="*60)
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
